package models

import (
	"time"

	tea "github.com/charmbracelet/bubbletea"

	"arien-ai/internal/config"
	"arien-ai/internal/ui/styles"
	"arien-ai/pkg/types"
)

// InteractiveOptions contains options for the interactive mode
type InteractiveOptions struct {
	SessionID        string
	ProviderOverride string
	ModelOverride    string
	SafeMode         bool
	NoHistory        bool
	DebugMode        bool
}

// ExitInfo contains information about the exit state
type ExitInfo struct {
	Message           string
	ShouldSaveSession bool
	HasErrors         bool
	SessionStats      *SessionStats
}

// SessionStats contains statistics about the session
type SessionStats struct {
	MessageCount int
	CommandCount int
	Duration     time.Duration
	TokenUsage   types.TokenUsage
}

// MainModel represents the main terminal interface model
type MainModel struct {
	configManager *config.Manager
	theme         *styles.Theme
	options       InteractiveOptions
	
	// Current session
	session *types.Session
	
	// UI state
	width  int
	height int
	
	// Components (to be implemented)
	// header    *HeaderComponent
	// chat      *ChatComponent
	// input     *InputComponent
	// status    *StatusComponent
	
	// Application state
	started   time.Time
	exitInfo  ExitInfo
}

// NewMainModel creates a new main terminal model
func NewMainModel(configManager *config.Manager, options InteractiveOptions) (*MainModel, error) {
	// Create new session
	config := configManager.Get()
	session := types.NewSession("Interactive Session", "CLI Terminal Session", config.LLM)
	
	return &MainModel{
		configManager: configManager,
		theme:         styles.GetTheme(config.UI.Theme),
		options:       options,
		session:       session,
		started:       time.Now(),
	}, nil
}

// Init initializes the main model
func (m *MainModel) Init() tea.Cmd {
	return nil
}

// Update handles messages and updates the model
func (m *MainModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil
		
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			m.prepareExit()
			return m, tea.Quit
		}
	}
	
	return m, nil
}

// View renders the main terminal interface
func (m *MainModel) View() string {
	if m.width == 0 {
		return "Loading..."
	}
	
	// For now, return a placeholder
	return "Arien-AI Terminal Interface\n\nPress Ctrl+C to exit"
}

// GetExitInfo returns information about the exit state
func (m *MainModel) GetExitInfo() ExitInfo {
	return m.exitInfo
}

// SaveCurrentSession saves the current session
func (m *MainModel) SaveCurrentSession() error {
	// TODO: Implement session saving
	return nil
}

// prepareExit prepares the exit information
func (m *MainModel) prepareExit() {
	duration := time.Since(m.started)
	
	stats := &SessionStats{
		MessageCount: len(m.session.Messages),
		CommandCount: len(m.session.CommandHistory),
		Duration:     duration,
		TokenUsage:   types.TokenUsage{
			TotalTokens: m.session.TokenUsage.TotalTokens,
		},
	}
	
	m.exitInfo = ExitInfo{
		Message:           "Goodbye! 👋",
		ShouldSaveSession: !m.options.NoHistory && len(m.session.Messages) > 0,
		HasErrors:         false, // TODO: Track errors
		SessionStats:      stats,
	}
}
