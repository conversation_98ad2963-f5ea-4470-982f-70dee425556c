package cmd

import (
	"context"
	"fmt"
	"os"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/spf13/cobra"

	"arien-ai/internal/llm"
	"arien-ai/internal/ui/models"
	"arien-ai/pkg/types"
)

// onboardingCmd represents the onboarding command
var onboardingCmd = &cobra.Command{
	Use:   "onboard",
	Short: "Initial setup and configuration",
	Long: `Run the initial setup process to configure Arien-AI.
This will guide you through setting up your LLM provider,
API keys, and preferences.`,
	Run: func(cmd *cobra.Command, args []string) {
		runOnboardingCommand(cmd, args)
	},
}

func init() {
	onboardingCmd.Flags().Bool("force", false, "Force onboarding even if already configured")
	onboardingCmd.Flags().String("provider", "", "Pre-select LLM provider (deepseek or ollama)")
	onboardingCmd.Flags().String("model", "", "Pre-select model")
	onboardingCmd.Flags().String("api-key", "", "Pre-set API key")
}

func runOnboardingCommand(cmd *cobra.Command, args []string) {
	runOnboarding()
}

func runOnboarding() {
	// Check if already configured (unless force flag is used)
	force, _ := onboardingCmd.Flags().GetBool("force")
	if !force && !configManager.IsFirstRun() {
		fmt.Println("Arien-AI is already configured.")
		fmt.Println("Use 'arien-ai onboard --force' to run setup again.")
		fmt.Println("Or use 'arien-ai interactive' to start using the system.")
		return
	}

	// Get pre-selected values from flags
	preProvider, _ := onboardingCmd.Flags().GetString("provider")
	preModel, _ := onboardingCmd.Flags().GetString("model")
	preAPIKey, _ := onboardingCmd.Flags().GetString("api-key")

	// Create onboarding model
	model := models.NewOnboardingModel(configManager, preProvider, preModel, preAPIKey)

	// Create and run the Bubble Tea program
	p := tea.NewProgram(model, tea.WithAltScreen())
	
	finalModel, err := p.Run()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error running onboarding: %v\n", err)
		os.Exit(1)
	}

	// Check if onboarding was completed successfully
	if onboardingModel, ok := finalModel.(*models.OnboardingModel); ok {
		if onboardingModel.IsCompleted() {
			fmt.Println("\n✅ Onboarding completed successfully!")
			
			// Validate the configuration
			if err := validateConfiguration(); err != nil {
				fmt.Fprintf(os.Stderr, "⚠️  Configuration validation failed: %v\n", err)
				fmt.Println("You can fix this later using 'arien-ai config' commands.")
			} else {
				fmt.Println("🎉 Configuration validated successfully!")
			}
			
			fmt.Println("\nYou can now start using Arien-AI with:")
			fmt.Println("  arien-ai interactive")
			
			// Auto-start interactive mode if requested
			if shouldAutoStart() {
				fmt.Println("\nStarting interactive mode...")
				time.Sleep(2 * time.Second)
				runInteractive()
			}
		} else {
			fmt.Println("\n❌ Onboarding was cancelled.")
			fmt.Println("You can run 'arien-ai onboard' again to complete the setup.")
		}
	}
}

func validateConfiguration() error {
	config := configManager.Get()
	
	// Validate LLM configuration
	if err := configManager.Validate(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}
	
	// Test LLM connection
	fmt.Println("Testing LLM connection...")
	
	client, err := llm.NewClient(config.LLM)
	if err != nil {
		return fmt.Errorf("failed to create LLM client: %w", err)
	}
	defer client.Close()
	
	// Test with a simple message
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	testMessages := []*types.Message{
		types.NewUserMessage("Hello, please respond with 'OK' to confirm the connection is working."),
	}
	
	response, err := client.Chat(ctx, testMessages, nil)
	if err != nil {
		return fmt.Errorf("LLM connection test failed: %w", err)
	}
	
	if response == nil {
		return fmt.Errorf("received empty response from LLM")
	}
	
	fmt.Printf("✅ LLM connection test successful: %s\n", response.Content[:min(50, len(response.Content))])
	
	return nil
}

func shouldAutoStart() bool {
	// Check if user wants to auto-start interactive mode
	// This could be a configuration option or prompt
	return false // For now, don't auto-start
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Helper functions for onboarding validation

func validateProvider(provider string) error {
	if !llm.IsProviderSupported(provider) {
		return fmt.Errorf("unsupported provider: %s", provider)
	}
	return nil
}

func validateModel(provider, model string) error {
	defaultModels := llm.GetDefaultModels()
	if models, exists := defaultModels[provider]; exists {
		for _, m := range models {
			if m == model {
				return nil
			}
		}
		return fmt.Errorf("model %s not found for provider %s", model, provider)
	}
	return fmt.Errorf("no models available for provider %s", provider)
}

func validateAPIKey(provider, apiKey string) error {
	if provider == "deepseek" && apiKey == "" {
		return fmt.Errorf("API key is required for Deepseek provider")
	}
	
	if provider == "deepseek" && len(apiKey) < 10 {
		return fmt.Errorf("API key appears to be too short")
	}
	
	return nil
}

func testOllamaConnection() error {
	// Test if Ollama is running
	config := types.LLMConfig{
		Provider: "ollama",
		Model:    "llama3.3",
		BaseURL:  "http://localhost:11434/v1",
	}
	
	client, err := llm.NewOllamaClient(config)
	if err != nil {
		return fmt.Errorf("failed to create Ollama client: %w", err)
	}
	defer client.Close()
	
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	// Try to get models to test connection
	_, err = client.GetModels(ctx)
	if err != nil {
		return fmt.Errorf("Ollama connection failed: %w", err)
	}
	
	return nil
}

func getAvailableOllamaModels() ([]string, error) {
	config := types.LLMConfig{
		Provider: "ollama",
		BaseURL:  "http://localhost:11434/v1",
	}
	
	client, err := llm.NewOllamaClient(config)
	if err != nil {
		return nil, err
	}
	defer client.Close()
	
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	models, err := client.GetModels(ctx)
	if err != nil {
		return nil, err
	}
	
	var modelNames []string
	for _, model := range models {
		modelNames = append(modelNames, model.ID)
	}
	
	return modelNames, nil
}
